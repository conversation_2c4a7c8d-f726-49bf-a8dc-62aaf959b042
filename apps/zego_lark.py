import signal
import time

from src.langgraph.common.init_log import init_log
from src.langgraph.zego_tools.ocean_executor import ocean_connection

from src.lark.zlark import zlark

init_log()


def signal_handler(signum, frame):
    """处理退出信号"""
    print("\n收到退出信号，正在停止...")
    zlark.stop_wss()
    exit(0)


if __name__ == "__main__":
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 初始化Lark
    zlark.init()

    # 启动后进行一次数据库连通自检（非阻塞）
    try:
        import asyncio

        async def _chk():
            ok = await ocean_connection.test_connection()
            print(f"DB connectivity: {'OK' if ok else 'FAILED'}")

        try:
            loop = asyncio.get_running_loop()
            loop.create_task(_chk())
        except RuntimeError:
            asyncio.run(_chk())
    except Exception as e:
        print(f"DB connectivity check error: {e}")

    print("Lark 客户端已启动，按 Ctrl+C 退出...")

    try:
        # 保持主线程运行
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n正在停止...")
        zlark.stop_wss()
