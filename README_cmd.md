### 命令速查（开发者）

- 开发环境
  - 要求：Python 3.12+
  - 安装依赖（建议使用 uv）
    ```bash
    uv pip install -e .[dev]
    ```
  - 配置环境变量：在项目根目录创建并填写 `.env`（参考 `README.md` 的“环境变量示例”）。

- 启动 LangGraph Dev（基于 `langgraph.json`）
  ```bash
  uv run langgraph dev
  ```
  - 默认监听 `http://localhost:2024`

- 本地运行示例（控制台流式输出）
  ```bash
  uv run python apps/zego_cli.py
  ```

- 飞书长连客户端（需准备 `src/lark/.env` 或修改 `zlark.py` 指向根目录 `.env`）
  ```bash
  uv run python apps/zego_lark.py
  ```

- 单元测试（SQL 生成）
  ```bash
  uv run bash tests/test_sqlgen.sh
  ```

- 其它测试/脚本
  - 生成每日 SQL 组合样例（便于巡检/对拍）
    ```bash
    uv run bash tests/generate_daily_sql.sh
    ```
  - 运行全部测试脚本（如有）
    ```bash
    uv run bash tests/runall.sh
    ```

- 数据库初始化与清理（开发用 Postgres/Store）
  ```bash
  bash scripts/init_pg.sh     # 初始化本地 Postgres（含 Store/Checkpoint）
  bash scripts/reinit_pg.sh   # 重新初始化（清空并重建）
  bash scripts/delete_pg.sh   # 清理
  ```

- 常用可配置项（来自 `.env`）
  - LLM Provider：见 `README.md`“环境变量示例（.env）”
  - 数据库（用于 `src/langgraph/zego_tools/ocean_executor`）：`ZEGO_MYSQL_*`
  - 仅数据库走 SOCKS5 代理：`ZEGO_SOCKS_*`
  - 飞书：`LARK_*`（注意 `src/lark/.env` 独立加载）

- 调试提示
  - 通过 `config={"configurable": {"thread_id": "your-session", "max_parallel_workers": 5}}` 隔离会话与 Store 命名空间并设置并发上限（`planner_router` 读取）。
  - SQL 执行失败会在 Store 中沉淀“错误码说明”，后续分析提示词会自动引用。
  - 需要查看运行时数据，可在 Postgres Store 中按 `("data_center","queried_data", thread_id)` 命名空间检索。


