### RAG 设计与经验库

```mermaid
flowchart LR
  subgraph Store["Postgres Store (LangGraph)"]
    NS1["命名空间: (data_center, queried_data, thread_id)"]
    NS2["命名空间: (experience)"]
  end

  subgraph DC["DataCenter 内容寻址"]
    GEN["generate_sql(query_params)"]
    HASH["sql → sha256 前16位"]
    KEY1["sql:hash 键存储 data + error_info"]
    MAP["map:param_key → sql_hash"]
  end

  GEN --> HASH --> KEY1 --> NS1
  MAP --> NS1

  subgraph Use["使用路径"]
    GET1["get_dataframe(param_key)"]
    HIT1["优先 param→sql 缓存"]
    FALL["回退 legacy param 键 (读取)"]
  end

  GET1 --> HIT1 --> NS1
  GET1 -. miss .-> FALL --> NS1

  subgraph RAG["经验库"]
    PUT["store_aput(namespace=(experience), key=exp_…)" ]
    SRCH["store_asearch(namespace=(experience), query)"]
  end

  PUT --> NS2
  SRCH --> NS2
```

- 存储与索引
  - 采用 LangGraph 的 `AsyncPostgresStore` 作为统一向量/KV 存储，初始化见 `src/langgraph/memory/store_utils.get_store_and_checkpointer()`。
  - 默认经验库命名空间前缀：`("experience",)`，跨环境/团队统一沉淀。
  - 嵌入模型由 `get_default_embedding_model()` 提供，索引维度在 `store` 初始化时配置（见 `index={"dims": 1024, ...}`）。

- 检索流程（当前无独立 memory 节点）
  - 由业务节点按需调用 `store_asearch(store, ("experience",), query, limit)` 进行经验检索。
  - 检索结果可直接拼入提示词或存入 `messages`，不再维护 `state.memory_tips` 字段。

- 经验沉淀（规划重规划/汇报阶段）
  - 当将要结束流程（无需继续分析），可在 `reporter` 生成 `final_report` 后，将“最终结论 + 少量上下文”整理为文本，调用 `store_aput(namespace=("experience",), key=exp_{timestamp})` 写入经验库，并按 `content` 建索引，便于下次检索。

- 经验入库（自定义/离线）
  - 可使用 `store_split_content_to_docs(long_content, metadata)` 将长文本切分为多个 `Document`，随后循环 `store_aput` 入库并建立 `index=["content"]`。

- 命名空间与会话隔离
  - 经验库使用全局 `("experience",)`；运行期数据则使用 `DataCenter` 的命名空间 `("data_center","queried_data", thread_id)`，二者相互独立。

- 检索质量建议
  - 为检索 query 设计“短小、概念明确”的关键词（由 LLM 自动生成）。
  - 定期沉淀“问题描述 + 关键信息 + 少量上下文”，避免噪声过大影响召回。
  - 可按场景补充 metadata（如业务线/地区/指标），未来可扩展基于 metadata 的过滤。


