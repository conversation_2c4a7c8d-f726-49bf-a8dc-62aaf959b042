"""
简化的数据库连接器 - Linus式设计

核心原则：
1. 一个连接，一个锁，排队执行
2. 只使用aiomysql，不要fallback
3. 简化SOCKS支持，不要复杂的contextvars
4. 消除所有特殊情况分支
"""

import asyncio
import logging
import os
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Optional

try:
    import aiomysql
except ImportError:
    aiomysql = None

# socks 处理由 socket_router 统一管理，不需要直接导入

import pandas as pd
from dotenv import load_dotenv

# 导入并启用socket路由
from src.langgraph.common.utils.socket_router import db_socks_context, ensure_socket_patched

from .types import SqlExecutorResult

# 加载环境变量（包括本地的.env文件）
load_dotenv()
load_dotenv(os.path.join(os.path.dirname(__file__), ".env"))

# 启用socket patching（与原版本保持一致）
ensure_socket_patched()

logger = logging.getLogger(__name__)


@dataclass
class DatabaseParams:
    """数据库连接参数"""

    name: str
    host_key: str
    port_key: str
    user_key: str
    password_key: str
    db_key: Optional[str] = None
    default_port: int = 3306


class SimpleDatabaseConnection:
    """简化的数据库连接器

    设计原则：
    - 单连接 + 全局锁 = 排队执行
    - 只用aiomysql，连接失败就直接报错
    - SOCKS支持但不复杂化
    - 没有连接池、熔断器等过度设计
    """

    def __init__(self, params: DatabaseParams):
        self.params = params
        self._connection: Optional[Any] = None
        self._lock = asyncio.Lock()  # 全局锁，确保排队执行
        self._last_error: Optional[str] = None

    async def execute_query(self, sql: str, themis_db: Optional[str] = None) -> SqlExecutorResult:
        """执行SQL查询 - 排队执行，一次只有一个查询"""
        if aiomysql is None:
            return SqlExecutorResult.failure("aiomysql未安装", "MISSING_DEPENDENCY")

        # 关键修复：Ocean连接的查询也需要在SOCKS上下文中执行
        if self.params.name == "ocean":
            async with db_socks_context():
                return await self._execute_query_internal(sql, themis_db)
        else:
            # Themis连接直接执行
            return await self._execute_query_internal(sql, themis_db)

    async def _execute_query_internal(self, sql: str, themis_db: Optional[str] = None) -> SqlExecutorResult:
        """内部查询执行逻辑"""
        start_time = datetime.now()

        # 全局锁确保排队执行
        async with self._lock:
            try:
                conn = await self._get_connection()
                if not conn:
                    return SqlExecutorResult.failure(self._last_error or "无法建立数据库连接", "CONNECTION_FAILED")

                async with conn.cursor() as cursor:
                    # 如果需要切换数据库（仅themis）
                    if themis_db:
                        await cursor.execute(f"USE `{themis_db}`")

                    await cursor.execute(sql)

                    # 获取结果
                    if cursor.description:
                        columns = [desc[0] for desc in cursor.description]
                        rows = await cursor.fetchall()
                        results = pd.DataFrame(rows, columns=columns)
                    else:
                        # INSERT/UPDATE/DELETE等无结果集的操作
                        results = pd.DataFrame()

                    query_time = (datetime.now() - start_time).total_seconds()
                    logger.info(
                        f"[{self.params.name}] 查询完成，返回 {len(results)} 条记录，" f"耗时 {query_time:.2f} 秒"
                    )

                    return SqlExecutorResult.success(results)

            except Exception as e:
                error_msg = str(e)
                logger.error(f"[{self.params.name}] 查询失败: {error_msg}\nSQL: {sql}")

                # 连接可能已断开，重置连接
                await self._close_connection()

                # 分类错误
                error_code = self._classify_error(error_msg)
                result = SqlExecutorResult.failure(error_msg, error_code)
                result.error_source = "simple_connector"
                return result

    async def _get_connection(self) -> Optional[Any]:
        """获取数据库连接 - 简单直接"""
        if self._connection is None:
            try:
                self._connection = await self._create_connection()
                if self._connection:
                    self._last_error = None
                    logger.info(f"[{self.params.name}] 数据库连接建立成功")
            except Exception as e:
                error_msg = f"创建连接失败: {str(e)}"
                self._last_error = error_msg
                logger.error(f"[{self.params.name}] {error_msg}")
                return None

        return self._connection

    async def _create_connection(self) -> Optional[Any]:
        """创建数据库连接 - 根据连接类型决定是否使用SOCKS"""
        host = os.getenv(self.params.host_key)
        port = int(os.getenv(self.params.port_key, str(self.params.default_port)))
        user = os.getenv(self.params.user_key)
        password = os.getenv(self.params.password_key)
        db = os.getenv(self.params.db_key) if self.params.db_key else None

        if not all([host, user]):
            raise ValueError(f"缺少必要的连接参数: host={host}, user={user}")

        # 密码可以为空（某些数据库配置）
        if password is None:
            password = ""

        # 根据连接类型决定是否使用SOCKS
        # Ocean连接需要SOCKS代理，Themis连接不能使用SOCKS
        if self.params.name == "ocean":
            # Ocean连接：在SOCKS上下文中创建连接
            async with db_socks_context():
                conn = await aiomysql.connect(
                    host=host,
                    port=port,
                    user=user,
                    password=password,
                    db=db,
                    charset="utf8mb4",
                    autocommit=True,
                    connect_timeout=30,
                )
        else:
            # Themis连接：直接连接，不使用SOCKS
            conn = await aiomysql.connect(
                host=host,
                port=port,
                user=user,
                password=password,
                db=db,
                charset="utf8mb4",
                autocommit=True,
                connect_timeout=30,
            )

        return conn

    async def _close_connection(self):
        """关闭连接"""
        if self._connection:
            try:
                self._connection.close()
            except Exception as e:
                logger.warning(f"[{self.params.name}] 关闭连接时出错: {e}")
            finally:
                self._connection = None

    def _classify_error(self, error_text: str) -> str:
        """简单的错误分类"""
        error_lower = error_text.lower()

        if "syntax error" in error_lower or "1064" in error_text:
            return "SQL_SYNTAX_ERROR"
        elif "access denied" in error_lower or "1045" in error_text:
            return "ACCESS_DENIED"
        elif "unknown database" in error_lower or "1049" in error_text:
            return "DATABASE_NOT_FOUND"
        elif "connection" in error_lower or "2003" in error_text:
            return "CONNECTION_ERROR"
        elif "timeout" in error_lower:
            return "TIMEOUT_ERROR"
        else:
            return "UNKNOWN_ERROR"

    async def test_connection(self, timeout_seconds: Optional[int] = None) -> bool:
        """测试连接

        Args:
            timeout_seconds: 超时时间（秒），如果不指定则使用默认的30秒连接超时
        """
        try:
            if timeout_seconds is not None:
                # 使用asyncio.wait_for来实现超时控制
                result = await asyncio.wait_for(self.execute_query("SELECT 1 as test"), timeout=timeout_seconds)
            else:
                result = await self.execute_query("SELECT 1 as test")

            return result.is_successful and len(result.data) > 0
        except asyncio.TimeoutError:
            logger.error(f"[{self.params.name}] 连接测试超时 ({timeout_seconds}秒)")
            return False
        except Exception as e:
            logger.error(f"[{self.params.name}] 连接测试失败: {e}")
            return False

    async def close(self):
        """关闭连接器"""
        await self._close_connection()


# 创建默认连接参数
def _build_ocean_params() -> DatabaseParams:
    return DatabaseParams(
        name="ocean",
        host_key="ZEGO_MYSQL_URL",
        port_key="ZEGO_MYSQL_PORT",
        user_key="ZEGO_MYSQL_USER",
        password_key="ZEGO_MYSQL_PASSWORD",
        db_key="ZEGO_MYSQL_DB",
    )


def _build_themis_params() -> DatabaseParams:
    return DatabaseParams(
        name="themis",
        host_key="THEMIS_URL",
        port_key="THEMIS_PORT",
        user_key="THEMIS_USER",
        password_key="THEMIS_PASSWORD",
        db_key=None,  # Themis 不需要默认数据库
    )


# 全局连接实例
ocean_connection = SimpleDatabaseConnection(_build_ocean_params())
themis_connection = SimpleDatabaseConnection(_build_themis_params())
