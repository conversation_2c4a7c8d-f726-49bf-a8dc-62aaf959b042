"""简化的SQL执行器 - Linus式设计

核心原则：
1. 简单的重试机制，不要复杂的指数退避
2. 直接使用简化的连接器
3. 清晰的错误处理，不要过度分类
"""

import asyncio
import logging
from typing import Literal, Optional

from .error_handler import extract_error_code_info, format_error_code_info_for_llm

from .simple_connector import ocean_connection, SimpleDatabaseConnection, themis_connection
from .types import SqlExecutorResult

logger = logging.getLogger(__name__)


async def execute_sql_async(
    type: Literal["ocean", "themis"],
    sql: str,
    max_retries: int = 2,  # 简化：最多重试2次就够了
    base_delay: float = 1.0,  # 兼容性参数，简化版本中忽略指数退避
    themis_db: Optional[str] = None,
) -> SqlExecutorResult:
    """
    简化的异步SQL执行器

    Args:
        type: 连接类型，"ocean" 或 "themis"
        sql: SQL查询语句
        max_retries: 最大重试次数，默认2次
        base_delay: 基础延迟时间（兼容性参数，简化版本使用线性退避）
        themis_db: 可选，仅当 type == "themis" 时指定要切换的库名

    Returns:
        SqlExecutorResult: 查询结果
    """
    connection: SimpleDatabaseConnection = ocean_connection if type == "ocean" else themis_connection
    return await _execute_with_retry(connection, sql, max_retries, base_delay, themis_db)


async def _execute_with_retry(
    connection: SimpleDatabaseConnection,
    sql: str,
    max_retries: int,
    base_delay: float,  # 兼容性参数，简化版本使用固定延迟
    themis_db: Optional[str] = None,
) -> SqlExecutorResult:
    """执行SQL查询的重试逻辑"""
    last_error = None
    db_type = connection.params.name

    # 简单重试：最多尝试 max_retries + 1 次
    for attempt in range(max_retries + 1):
        try:
            result = await connection.execute_query(sql, themis_db)

            if result.is_successful:
                # 成功时提取错误码信息（业务逻辑需要）
                if result.data is not None:
                    error_info = extract_error_code_info(result.data)
                    error_info_text = format_error_code_info_for_llm(error_info)
                    result.additional_info = error_info_text

                logger.info(f"[{db_type}] SQL执行成功 (尝试 {attempt + 1}/{max_retries + 1})")
                return result
            else:
                # 失败时判断是否应该重试
                if not _should_retry(result.error_code):
                    logger.info(f"[{db_type}] 错误不可重试: {result.error_code}")
                    return result

                last_error = result
                if attempt < max_retries:
                    wait_time = 1.0 * (attempt + 1)  # 简单的线性退避：1s, 2s, 3s...
                    logger.warning(
                        f"[{db_type}] SQL执行失败，{wait_time}秒后重试 "
                        f"(尝试 {attempt + 1}/{max_retries + 1}): {result.error_message}"
                    )
                    await asyncio.sleep(wait_time)

        except Exception as e:
            error_msg = f"执行器异常: {str(e)}"
            logger.error(f"[{db_type}] {error_msg}")
            last_error = SqlExecutorResult.failure(error_msg, "EXECUTOR_ERROR")
            last_error.error_source = "simple_executor"

            if attempt < max_retries:
                wait_time = 1.0 * (attempt + 1)
                logger.warning(f"[{db_type}] 执行器异常，{wait_time}秒后重试")
                await asyncio.sleep(wait_time)

    # 所有重试都失败了
    if last_error:
        logger.error(f"[{db_type}] SQL执行最终失败: {last_error.error_message}\nSQL: {sql}")
        return last_error
    else:
        return SqlExecutorResult.failure("未知错误", "UNKNOWN_ERROR")


def _should_retry(error_code: Optional[str]) -> bool:
    """判断错误是否应该重试 - 简化版本"""
    if not error_code:
        return True  # 未知错误，尝试重试

    # 不应该重试的错误类型
    non_retryable_errors = {
        "SQL_SYNTAX_ERROR",  # SQL语法错误
        "ACCESS_DENIED",  # 权限错误
        "DATABASE_NOT_FOUND",  # 数据库不存在
        "MISSING_DEPENDENCY",  # 依赖缺失
    }

    return error_code not in non_retryable_errors
