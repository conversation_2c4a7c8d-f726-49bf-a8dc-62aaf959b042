"""SQL执行器类型定义模块

这个模块定义了SQL执行器使用的类型，避免循环导入问题。
"""

from typing import Optional

import pandas as pd
from pydantic import BaseModel, ConfigDict, Field


class SqlExecutorResult(BaseModel):
    """SQL执行器结果类，统一所有SQL执行的返回值"""

    data: Optional[pd.DataFrame] = Field(default=None, description="查询结果数据")
    error_message: Optional[str] = Field(default=None, description="错误信息")
    error_code: Optional[str] = Field(default=None, description="错误代码")
    additional_info: Optional[str] = Field(default=None, description="额外信息（如错误码说明）")
    error_source: Optional[str] = Field(default=None, description="错误来源组件")
    is_successful: bool = Field(default=False, description="是否成功")

    model_config = ConfigDict(arbitrary_types_allowed=True)

    def __init__(self, **data):
        super().__init__(**data)
        # 自动判断是否成功
        if not hasattr(self, "is_successful") or "is_successful" not in data:
            self.is_successful = self.data is not None and self.error_message is None

    @classmethod
    def success(cls, data: pd.DataFrame, additional_info: str = "") -> "SqlExecutorResult":
        """创建成功结果"""
        return cls(data=data, additional_info=additional_info, is_successful=True)

    @classmethod
    def failure(cls, error_message: str, error_code: str = "UNKNOWN_ERROR") -> "SqlExecutorResult":
        """创建失败结果"""
        return cls(error_message=error_message, error_code=error_code, is_successful=False)

    def zego_model_dump_json(self) -> str:
        return self.model_dump_json(include={"is_successful"})
