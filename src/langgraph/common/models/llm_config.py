"""
LLM配置系统 - 简化重试架构

设计原则：
1. 模式分类：json_mode 和 text_mode
2. 数组索引优先级：0最优先，index越大模型越高级
3. 简单重试逻辑：失败2次重试更高级模型
4. 智能错误处理：某些错误立即切换模型
5. 开发者友好：直接修改数组配置即可调试
"""

import logging
from enum import Enum
from typing import Any, Optional

from langchain_core.language_models import BaseChatModel

logger = logging.getLogger(__name__)


# ModelTier 枚举已废弃，不再需要


class RequestMode(Enum):
    """请求模式枚举"""

    JSON_MODE = "json_mode"  # 结构化输出请求
    TEXT_MODE = "text_mode"  # 文本输出请求


def _get_model_config_by_mode(mode: RequestMode) -> list[dict[str, Any]]:
    """根据模式获取模型配置数组"""
    from src.langgraph.common.models.new_llms import active_json_models, active_text_models

    if mode == RequestMode.JSON_MODE:
        return active_json_models
    elif mode == RequestMode.TEXT_MODE:
        return active_text_models
    else:
        raise ValueError(f"Unknown request mode: {mode}")


def _get_model_index_from_retry(retry_index: int) -> int:
    """根据重试索引计算模型索引

    每个模型失败2次后切换到下一个模型：
    - retry_index 0, 1: 使用第0个模型
    - retry_index 2, 3: 使用第1个模型
    - retry_index 4, 5: 使用第2个模型
    """
    return retry_index // 2


def get_model_config(mode: RequestMode, retry_index: int = 0) -> Optional[dict]:
    """
    获取模型配置 - 直接返回配置字典，避免字符串解析

    Args:
        mode: 请求模式
        retry_index: 重试索引，用于确定使用哪个模型

    Returns:
        模型配置字典，包含 provider、model、params，如果没有可用模型则返回None
    """
    try:
        model_configs = _get_model_config_by_mode(mode)
        if not model_configs:
            logger.warning(f"No models configured for mode: {mode.value}")
            return None

        model_index = _get_model_index_from_retry(retry_index)
        if model_index >= len(model_configs):
            logger.warning(f"No more models available after retry #{retry_index} in {mode.value} mode")
            return None

        config = model_configs[model_index]

        if retry_index > 0:
            logger.info(f"Retry #{retry_index}: Using model {config['provider']}:{config['model']}")

        return config

    except Exception as e:
        logger.error(f"Error getting model config: {e}")
        return None


# get_model_for_request 函数已废弃，直接使用 get_llm_instance


def get_max_retries(mode: RequestMode) -> int:
    """获取指定模式的最大重试次数（不含第一次尝试）

    计算方式：模型数量 × 2 - 1（每个模型尝试2次）
    """
    try:
        model_configs = _get_model_config_by_mode(mode)
        if not model_configs:
            return 0
        return len(model_configs) * 2 - 1
    except Exception as e:
        logger.error(f"Error getting max retries: {e}")
        return 0


def get_llm_instance(mode: RequestMode, retry_index: int = 0) -> Optional[BaseChatModel]:
    """直接获取配置好的LLM实例

    Args:
        mode: 请求模式
        retry_index: 重试索引

    Returns:
        配置好的LLM实例，如果失败则返回None
    """
    try:
        config = get_model_config(mode, retry_index)
        if not config:
            return None

        provider = config["provider"]
        model_name = config["model"]
        extra_params = config.get("params", {})

        from src.langgraph.common.models.new_llms import get_llm_by_code

        llm_instance = get_llm_by_code(provider, model_name, extra_params)

        return llm_instance

    except Exception as e:
        logger.error(f"Error creating LLM instance: {e}")
        return None


def should_immediately_switch_model(error: Exception) -> bool:
    """
    判断是否应该立即切换模型而不是重试

    Args:
        error: 异常对象

    Returns:
        True表示应该立即切换模型，False表示可以重试
    """
    error_str = str(error).lower()

    # Token限制相关错误
    token_limit_indicators = [
        "range of input length should be",
        "input length",
        "token limit",
        "context length",
        "maximum context",
        "too many tokens",
        "input too long",
    ]

    # 模型不支持的功能
    unsupported_indicators = ["not supported", "invalid parameter", "unsupported", "not available"]

    # 检查是否是应该立即切换的错误
    for indicator in token_limit_indicators + unsupported_indicators:
        if indicator in error_str:
            return True

    return False
