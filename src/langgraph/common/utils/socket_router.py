"""Socket routing with contextvars

This module provides per-coroutine socket routing between plain sockets and
SOCKS5 proxied sockets using contextvars, avoiding any global monkey-patching
that toggles process-wide state at runtime.

Usage:
- Call ensure_socket_patched() once during startup (idempotent).
- Wrap DB operations with `db_socks_context()` so sockets created inside use SOCKS.
- Wrap LLM operations with `plain_socket_context()` to ensure plain sockets.

The default is plain sockets.
"""

from __future__ import annotations

import contextvars

import os
import socket as _socket
from contextlib import asynccontextmanager
from typing import Any, Optional, Tuple

try:
    import socks  # type: ignore
except Exception:  # pragma: no cover - socks is optional at import, env may not need it
    socks = None  # type: ignore


# Per-context flag: whether to use SOCKS for newly created sockets
_use_socks_var: contextvars.ContextVar[bool] = contextvars.ContextVar("_use_socks_var", default=False)


def _get_proxy_config() -> Tuple[Optional[str], Optional[int], Optional[str], Optional[str]]:
    """Load SOCKS proxy config from env at call-time to reflect updates."""
    host = os.getenv("ZEGO_SOCKS_PROXY")
    port_str = os.getenv("ZEGO_SOCKS_PORT")
    username = os.getenv("ZEGO_SOCKS_PROXY_USER")
    password = os.getenv("ZEGO_SOCKS_PROXY_PASSWORD")
    port: Optional[int] = None
    if port_str:
        try:
            port = int(port_str)
        except Exception:
            port = None
    return host, port, username, password


_original_socket_ctor = _socket.socket
_original_create_connection = _socket.create_connection


def _make_socket(*args: Any, **kwargs: Any):
    """Create a socket honoring the current context routing.

    If SOCKS is enabled for the current coroutine and proxy config is valid,
    create a socks.socksocket with per-socket proxy. Otherwise, create a plain socket.
    """
    use_socks = _use_socks_var.get()
    host, port, username, password = _get_proxy_config()
    if use_socks and bool(host and port) and socks is not None:
        s = socks.socksocket(*args, **kwargs)  # type: ignore[attr-defined]
        # Configure per-socket proxy with remote DNS
        s.set_proxy(  # type: ignore[call-arg]
            socks.SOCKS5,  # type: ignore[attr-defined]
            host,
            port,
            username=username,
            password=password,
            rdns=True,
        )
        return s
    # Fallback to plain socket
    return _original_socket_ctor(*args, **kwargs)


_patched = False


def ensure_socket_patched() -> None:
    """Idempotently patch socket.socket to route by contextvars.

    We replace `socket.socket` with a callable that returns either a plain
    socket or a socks.socksocket configured per-socket. We do NOT set any
    global default proxy in the socks module.
    """
    global _patched
    if _patched:
        return
    # Patch constructors used by standard libs and PyMySQL/aiomysql paths
    _socket.socket = _make_socket  # type: ignore[assignment]

    def _create_connection(address, timeout=_socket._GLOBAL_DEFAULT_TIMEOUT, source_address=None):
        """Context-aware replacement for socket.create_connection.

        If SOCKS is enabled in this coroutine, create a socks.socksocket and connect
        to the given host:port using remote DNS; otherwise delegate to original.
        """
        host, port = address
        use_socks = _use_socks_var.get()
        proxy_host, proxy_port, username, password = _get_proxy_config()

        if use_socks and bool(proxy_host and proxy_port) and socks is not None:
            s = socks.socksocket()
            s.set_proxy(  # type: ignore[attr-defined]
                socks.SOCKS5,
                proxy_host,
                proxy_port,
                username=username,
                password=password,
                rdns=True,
            )
            # Apply a sensible default timeout if caller did not provide one
            if timeout is _socket._GLOBAL_DEFAULT_TIMEOUT:
                try:
                    default_timeout = int(os.getenv("SOCKS_CONNECT_TIMEOUT", os.getenv("DB_CONNECT_TIMEOUT", "30")))
                except Exception:
                    default_timeout = 30
                s.settimeout(default_timeout)
            else:
                s.settimeout(timeout)
            if source_address:
                s.bind(source_address)
            # For RDNS to take effect, pass the hostname string (do not pre-resolve)
            s.connect((host, port))
            return s
        # Fallback to original behavior
        return _original_create_connection(address, timeout, source_address)

    _socket.create_connection = _create_connection  # type: ignore[assignment]

    # 关键修复：Patch当前运行的事件循环实例的create_connection方法
    # 不然fastapi有问题
    import asyncio

    # 尝试patch当前运行的事件循环实例（如果有的话）
    try:
        current_loop = asyncio.get_running_loop()
        _patch_loop_instance(current_loop)
    except RuntimeError:
        # 没有运行中的事件循环，这在API服务启动时是正常的
        pass

    _patched = True


def _patch_loop_instance(loop):
    """Patch一个特定的事件循环实例"""
    if hasattr(loop, "_socks_patched"):
        return  # 已经patch过了

    _original_loop_instance_create_connection = loop.create_connection

    async def _loop_instance_create_connection(protocol_factory, host=None, port=None, **kwargs):
        """Context-aware replacement for loop.create_connection."""
        if _use_socks_var.get(False) and host and port:
            proxy_host, proxy_port, username, password = _get_proxy_config()

            if proxy_host and proxy_port and socks is not None:
                try:
                    sock = socks.socksocket()
                    sock.set_proxy(
                        socks.SOCKS5,
                        proxy_host,
                        proxy_port,
                        username=username,
                        password=password,
                        rdns=True,
                    )
                    sock.connect((host, port))
                    sock.setblocking(False)

                    return await _original_loop_instance_create_connection(protocol_factory, sock=sock, **kwargs)
                except Exception:
                    # SOCKS连接失败，回退到原始连接
                    pass

        return await _original_loop_instance_create_connection(protocol_factory, host, port, **kwargs)

    loop.create_connection = _loop_instance_create_connection
    loop._socks_patched = True


@asynccontextmanager
async def db_socks_context():
    """Enable SOCKS for sockets created within this async context."""
    token = _use_socks_var.set(True)
    try:
        yield
    finally:
        _use_socks_var.reset(token)


@asynccontextmanager
async def plain_socket_context():
    """Force plain sockets within this async context (explicitly disable SOCKS)."""
    token = _use_socks_var.set(False)
    try:
        yield
    finally:
        _use_socks_var.reset(token)
