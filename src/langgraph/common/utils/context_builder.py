from __future__ import annotations

from typing import List, Optional

from langchain_core.messages import BaseMessage, HumanMessage, SystemMessage
from pydantic import BaseModel

from src.langgraph.common.utils.time_utils import get_prompt_timestamp

from src.langgraph.tasks.planner_utils import get_deps_tasks, get_results_by_tasks


class ContextBuilder:
    """统一的上下文构建器：管理整个LLM对话上下文

    重构后的设计：
    - SystemMessage: 角色定义、规则、上下文信息、提示词片段
    - HumanMessage: 当前任务的具体请求
    - 集成原 PromptBuilder 的功能，统一管理所有消息构建
    """

    def __init__(self, *, state) -> None:
        self.state = state

        # 系统消息部分（原 PromptBuilder 功能）
        self._system_parts: List[str] = []

        # 上下文部分（原 ContextBuilder 功能）
        self._context_sections: List[str] = []

        # 人类消息部分
        self._human_task: Optional[str] = None

        # 自动添加用户问题到上下文
        self._add_user_questions_to_context()

    def _add_user_questions_to_context(self) -> None:
        """自动添加用户问题到上下文"""
        messages: List[BaseMessage] = self.state.get("messages", [])
        human_msgs = [msg for msg in messages if isinstance(msg, HumanMessage)]
        if human_msgs:
            for msg in human_msgs:
                self._context_sections.append(f"<{'用户问题'}>\n{str(msg.content)}\n</{'用户问题'}>")

    # ===== 系统消息构建方法（原 PromptBuilder 功能）=====
    def add_system_text(self, text: str | None) -> "ContextBuilder":
        """添加系统提示文本"""
        if text:
            self._system_parts.append(text.strip("\n"))
        return self

    def add_system_section(self, title: str, body: str) -> "ContextBuilder":
        """添加系统提示段落"""
        content = f"{title}\n{body}"
        return self.add_system_text(content)

    def add_json_schema(self, schema: BaseModel, extra_notes: str | None = None) -> "ContextBuilder":
        """添加JSON Schema输出要求"""
        schema_str = schema.model_json_schema()
        notes = f"\n重要：\n{extra_notes.strip()}" if extra_notes else ""
        block = "**输出格式**:\n" "以纯json格式输出, schema如下:\n" "```\n" f"{schema_str}\n" "```" + notes
        return self.add_system_text(block)

    def add_base_analysis_role(self) -> "ContextBuilder":
        """添加通用分析角色"""
        from src.langgraph.common.prompts.fragments import (
            UNIVERSAL_ANALYSIS_OUTPUT,
            UNIVERSAL_ANALYSIS_ROLE,
            UNIVERSAL_ANALYSIS_STRATEGY,
            UNIVERSAL_ANALYSIS_TASKS,
        )

        self.add_system_text(UNIVERSAL_ANALYSIS_ROLE)
        self.add_system_text(UNIVERSAL_ANALYSIS_TASKS)
        self.add_system_text(UNIVERSAL_ANALYSIS_STRATEGY)
        self.add_system_text(UNIVERSAL_ANALYSIS_OUTPUT)
        return self

    def add_ocean_dimension_tips(self) -> "ContextBuilder":
        """添加表格维度说明"""
        from src.langgraph.common.prompts.fragments import TABLE_DIMENSION_EXPLANATION

        self.add_system_text(TABLE_DIMENSION_EXPLANATION)
        return self

    def add_rtc_special_issue(self) -> "ContextBuilder":
        """添加特殊区域问题提示"""
        from src.langgraph.common.prompts.fragments import SPECIAL_REGION_ISSUE_NOTES

        self.add_system_text(SPECIAL_REGION_ISSUE_NOTES)
        return self

    def add_reporter_base(self) -> "ContextBuilder":
        """添加汇报角色基础设置"""
        from src.langgraph.common.prompts.fragments import (
            REPORTER_OUTPUT,
            REPORTER_PRINCIPLES,
            REPORTER_ROLE,
            REPORTER_TASKS,
        )

        self.add_system_text(REPORTER_ROLE)
        self.add_system_text(REPORTER_TASKS)
        self.add_system_text(REPORTER_PRINCIPLES)
        self.add_system_text(REPORTER_OUTPUT)
        return self

    # ===== 上下文构建方法（原 ContextBuilder 功能）=====
    def add_plan_goal(self, plan=None, title: str = "任务目标") -> "ContextBuilder":
        """添加计划目标到上下文"""
        if plan is None:
            plans = self.state.get("plans", []) or []
            plan = plans[-1] if plans else None
        goal = getattr(plan or object(), "goal", None)
        if goal:
            self._context_sections.append(f"<{title}>\n{str(goal)}\n</{title}>")
        return self

    def add_plan_thinking(self, plan=None, title: str = "规划阶段思考") -> "ContextBuilder":
        """添加规划思考到上下文"""
        if plan is None:
            plans = self.state.get("plans", []) or []
            plan = plans[-1] if plans else None
        thinking = getattr(plan or object(), "thinking", None)
        if thinking:
            self._context_sections.append(f"<{title}>\n{str(thinking)}\n</{title}>")
        return self

    def add_relate_dimension_summary(
        self,
        plan=None,
        *,
        current_work_id: Optional[int] = None,
        title: str = "分析摘要",
        max_count: int = 0,
    ) -> "ContextBuilder":
        """添加相关维度分析摘要到上下文"""
        if plan is None:
            plans = self.state.get("plans", []) or []
            plan = plans[-1] if plans else None

        from src.langgraph.nodes.common.types import WorkerParams, WorkerResult  # lazy import

        worker_results: dict[str, WorkerResult] = self.state.get("worker_results", {}) or {}
        wp_results: dict[str, WorkerParams] = self.state.get("worker_query_contexts", {}) or {}

        def build_line(key: str, info: WorkerResult) -> str:
            try:
                wp = wp_results.get(key)
                if wp:
                    # 使用新的组合方法创建完整消息
                    ai = WorkerResult.create_combined_message(worker_result=info, worker_params=wp)
                else:
                    # 如果没有查询上下文，只使用分析结果
                    ai = info.to_message()
                display = getattr(ai, "name", None) or "分析结果"
                return "\n".join([f"- 主题: {display}", str(getattr(ai, "content", ""))])
            except Exception:
                return ""

        lines: List[str] = []

        if current_work_id is None:
            # 所有结果
            for k, info in worker_results.items():
                try:
                    line = build_line(k, info)
                    if line:
                        lines.append(line)
                except Exception:
                    continue
        else:
            # 仅依赖任务的维度结果
            deps_tasks = get_deps_tasks(plan, current_work_id)
            dep_results = get_results_by_tasks(deps_tasks, worker_results)
            # 注意：get_results_by_tasks 返回的列表不包含 key，这里回退为全量匹配
            for k, info in worker_results.items():
                if info in dep_results:
                    try:
                        line = build_line(k, info)
                        if line:
                            lines.append(line)
                    except Exception:
                        continue

        if lines:
            if isinstance(max_count, int) and max_count > 0:
                lines = lines[:max_count]
            self._context_sections.append(f"<{title}>\n" + "\n".join(lines) + f"\n</{title}>")
        return self

    def add_raw_section(self, title: str, content: str) -> "ContextBuilder":
        """添加原始内容段落到上下文"""
        if content:
            self._context_sections.append(f"<{title}>\n{content}\n</{title}>")
        return self

    # ===== 人类消息构建方法 =====
    def set_human_task(self, task: str) -> "ContextBuilder":
        """设置人类任务请求"""
        self._human_task = task
        return self

    # ===== 消息构建输出方法 =====
    def build_messages(self) -> List[BaseMessage]:
        """构建完整的消息列表

        Returns:
            包含 SystemMessage 和 HumanMessage 的消息列表
        """
        messages: List[BaseMessage] = []

        # 1. 构建系统消息
        system_content_parts = []

        # 添加上下文信息
        if self._context_sections:
            context_text = "\n\n".join(self._context_sections)
            system_content_parts.append(context_text)

        # 添加系统提示词
        if self._system_parts:
            # 添加时间戳
            ts = get_prompt_timestamp()
            self._system_parts.append(f"> 当前时间：{ts}")

            system_prompt = "\n\n".join([p for p in self._system_parts if p])
            system_content_parts.append(system_prompt)

        # 创建系统消息
        if system_content_parts:
            system_content = "\n\n".join(system_content_parts)
            messages.append(SystemMessage(content=system_content))

        # 2. 构建人类消息
        if self._human_task:
            messages.append(HumanMessage(content=self._human_task))
        else:
            # 如果没有明确的人类任务，使用默认请求
            messages.append(HumanMessage(content="请根据以上信息进行分析。"))

        return messages


# 向后兼容方法已删除，保持代码简洁


__all__ = ["ContextBuilder"]
