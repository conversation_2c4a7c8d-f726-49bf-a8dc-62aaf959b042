from datetime import datetime, timezone
from typing import Optional, Union


def get_english_timestamp() -> str:
    return datetime.now().strftime("%a %b %d %Y %H:%M:%S %z")


def get_prompt_timestamp(ns: bool = False) -> str:
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f" if ns else "%Y-%m-%d %H:%M:%S")


def get_api_timestamp() -> str:
    return get_prompt_timestamp(ns=True)


def get_ns_timestamp() -> str:
    return get_prompt_timestamp(ns=True)


def get_str_timestamp() -> str:
    return datetime.now().strftime("%Y%m%d_%H%M%S_%f")


def _parse_datetime(value: Union[str, datetime, None]) -> Optional[datetime]:
    """
    将字符串/日期时间统一解析为带时区的 datetime 对象。

    解析规则：
    - 支持 ISO8601（带/不带微秒，带 Z 或 +00:00）
    - 对于无时区信息的时间，默认按 UTC 处理
    - 解析失败返回 None
    """
    if value is None:
        return None
    if isinstance(value, datetime):
        dt = value
    elif isinstance(value, str):
        if value.strip() == "--":
            return None
        s = value.strip()
        try:
            # 兼容以 Z 结尾的 ISO 字符串
            if s.endswith("Z"):
                s = s[:-1] + "+00:00"
            dt = datetime.fromisoformat(s)
        except Exception:
            # 兜底尝试常见格式
            for fmt in [
                "%Y-%m-%d %H:%M:%S.%f",
                "%Y-%m-%d %H:%M:%S",
                "%Y/%m/%d %H:%M:%S",
                "%Y-%m-%d",
            ]:
                try:
                    dt = datetime.strptime(s, fmt)
                    break
                except Exception:
                    dt = None
            if dt is None:
                return None
    else:
        return None

    if dt.tzinfo is None:
        # 无时区信息时，按 UTC 处理后再做本地化
        dt = dt.replace(tzinfo=timezone.utc)
    return dt


def format_human_datetime(value: Union[str, datetime, None]) -> str:
    """
    将时间格式化为人类可读短格式：YYYY-MM-DD HH:MM（本地时区）。
    解析失败返回原值或 "--"。
    """
    dt = _parse_datetime(value)
    if dt is None:
        return value if isinstance(value, str) and value.strip() else "--"
    # 转换到本地时区并格式化
    return dt.astimezone().strftime("%Y-%m-%d %H:%M")
