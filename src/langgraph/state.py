"""Define the state structures for the agent."""

from __future__ import annotations

import operator
from pathlib import Path
from typing import Dict, List, Optional, TypedDict

from langchain_core.messages import AnyMessage
from langchain_core.utils._merge import merge_obj
from typing_extensions import Annotated

from langgraph.graph import add_messages
from src.langgraph.nodes.common.types import *


def append_plans(left: Optional[List[DAGPlan]], right: Optional[List[DAGPlan]]) -> List[DAGPlan]:
    """聚合器：将两个计划列表按顺序合并（None 视为空）。"""
    return list(left or []) + list(right or [])


class ConfigSchema(TypedDict):
    """运行期可配置但可序列化的配置项（不可放句柄/客户端）。

    建议放置：来自用户/CLI/ENV 的可观测/可复现实验参数。
    """

    workspace_path: Path
    max_parallel_workers: int


class State(TypedDict):
    """State for the agent system, extends MessagesState with next field."""

    messages: Annotated[list[AnyMessage], add_messages]
    total_input_tokens: Annotated[int, operator.add]
    total_output_tokens: Annotated[int, operator.add]

    # 规划信息：按时间顺序保存的计划列表（并行节点合并时直接追加）
    plans: Annotated[List[DAGPlan], append_plans]

    # 当前 worker 节点处理的任务（在 Send 状态中传递）
    current_task: Optional[DAGPlanTask]

    # Worker 结果（通用，非仅维度）
    worker_results: Annotated[Dict[str, WorkerResult], merge_obj]

    # 工作参数存储，用于跟踪查询状态（包括成功和失败）
    worker_params_results: Annotated[Dict[str, WorkerParams], merge_obj]

    # Reporter 节点最终结构化汇报
    reporter_result: Optional[ReporterResult]

    @staticmethod
    def to_json(state: State) -> str:
        return state.model_dump_json(exclude={"worker_params_results.sql_result"})