import time
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

from src.langgraph.state import State

from src.lark.card.elements import card_element_markdown, card_element_zhedie


@dataclass
class NotifyConfig:
    debounce_ms: int = 1500
    max_updates_per_minute: int = 10
    topn_recent_results: int = 5
    max_thinking_len: int = 500
    max_content_len: int = 2000


def _truncate(text: str, max_len: int) -> str:
    if not text:
        return ""
    return text if len(text) <= max_len else text[: max_len - 1] + "…"


class GraphLarkNotifier:
    """
    基于 LangGraph 的完整 state 渲染并节流更新同一张飞书进度卡（与 message/node 解耦）。

    使用方式：
    - notifier = GraphLarkNotifier(zlark, thread_message_id)
    - await notifier.update_state(state)  # 传入完整 state（dict 或 State 对象均可）
    - await notifier.flush(force=True)  # 结束时收尾刷新
    """

    def __init__(self, zlark_instance, thread_message_id: str, config: Optional[NotifyConfig] = None):
        self.zlark = zlark_instance
        self.thread_message_id = thread_message_id
        self.config = config or NotifyConfig()

        # 运行期
        self._progress_message_id: Optional[str] = None
        self._latest_state: Optional[Dict[str, Any] | State] = None
        self._last_flush_ts: float = 0.0
        self._updates_in_minute: List[float] = []
        # 单协程串行入，锁不再需要；如未来演进为多协程并发，可再引入

    async def update_state(self, state: Dict[str, Any] | State):
        """接收完整 state，并尝试按节流策略更新卡片。"""
        self._latest_state = state
        if self._should_flush():
            await self.flush()

    def _should_flush(self) -> bool:
        now = time.time()
        # 清理一分钟窗口
        self._updates_in_minute = [t for t in self._updates_in_minute if now - t < 60]
        if len(self._updates_in_minute) >= self.config.max_updates_per_minute:
            return False  # 超配额，跳过本轮

        if (now - self._last_flush_ts) * 1000 < self.config.debounce_ms:
            return False  # 尚未到达节流窗口

        return True

    def _ensure_progress_card(self) -> str:
        if self._progress_message_id:
            return self._progress_message_id
        # 首次在原消息线程回复一张进度卡
        elements = [card_element_markdown("初始化进度...")]
        mid = self.zlark.reply_custom_card_msg(
            title="任务进度",
            subtitle="初始化",
            elements=elements,
            message_id=self.thread_message_id,
            reply_in_thread=True,
        )
        # 若 SDK 返回 None，则保底用线程 id 继续（更新会失败，但不影响后续）
        self._progress_message_id = mid or self.thread_message_id
        return self._progress_message_id

    # ---- State 解析工具 ----
    @staticmethod
    def _safe_get(obj: Any, attr: str, default: Any = None) -> Any:
        try:
            if isinstance(obj, dict):
                return obj.get(attr, default)
            return getattr(obj, attr, default)
        except Exception:
            return default

    def _get_latest_plan(self, state: Dict[str, Any] | State) -> Any:
        plans = self._safe_get(state, "plans", []) or []
        return plans[-1] if plans else None

    def _get_status_counts(self, plan: Any) -> Dict[str, int]:
        counts = {"READY": 0, "PENDING": 0, "RUNNING": 0, "DONE": 0, "FAILED": 0}
        tasks = self._safe_get(plan, "tasks", []) or []
        for t in tasks:
            st = str(self._safe_get(t, "status", "PENDING"))
            if st in counts:
                counts[st] += 1
        return counts

    def _get_goal(self, plan: Any) -> str:
        return str(self._safe_get(plan, "goal", ""))

    def _get_plan_thinking(self, plan: Any) -> str:
        raw = self._safe_get(plan, "thinking", "") or ""
        return _truncate(str(raw), self.config.max_thinking_len)

    def _iter_worker_results(self, state: Dict[str, Any] | State) -> List[tuple[str, str]]:
        results = self._safe_get(state, "worker_results", {}) or {}
        params_map = self._safe_get(state, "worker_query_contexts", {}) or {}
        items: List[tuple[str, str]] = []
        # Python3.7+ 字典保持插入顺序；如来源于合并不可控，则顺序不保证，但不影响展示
        for key, res in list(results.items())[-self.config.topn_recent_results :]:
            # 展示名称优先 query_title/metric_name
            wp = params_map.get(key)
            qp = self._safe_get(wp or {}, "query_params")
            name = self._safe_get(qp or {}, "query_title") or self._safe_get(qp or {}, "metric_name") or key
            conclusion = self._safe_get(res, "objective_conclusion", "") or ""
            items.append((str(name), _truncate(str(conclusion), 200)))
        return items

    def _get_reporter_summary(self, state: Dict[str, Any] | State) -> Optional[str]:
        rr = self._safe_get(state, "reporter_result")
        if not rr:
            return None
        summary = self._safe_get(rr, "executive_summary", "") or ""
        return _truncate(str(summary), 400) or None

    def _get_last_message_detail(self, state: Dict[str, Any] | State) -> Optional[str]:
        msgs = self._safe_get(state, "messages", []) or []
        if not msgs:
            return None
        last = msgs[-1]
        content = self._safe_get(last, "content", "") or ""
        return _truncate(str(content), self.config.max_content_len) or None

    def _build_progress_elements_from_state(self, state: Dict[str, Any] | State | None) -> List[dict]:
        if not state:
            return [card_element_markdown("(无更新)")]

        elements: List[dict] = []

        # 目标与进度统计
        plan = self._get_latest_plan(state)
        if plan is not None:
            goal = self._get_goal(plan)
            counts = self._get_status_counts(plan)
            total = sum(counts.values())
            summary_lines = [
                f"**目标**: {goal}" if goal else None,
                f"**进度**: 总数 {total} | READY {counts['READY']} | RUNNING {counts['RUNNING']} | DONE {counts['DONE']} | FAILED {counts['FAILED']} | PENDING {counts['PENDING']}",
            ]
            elements.append(card_element_markdown("\n".join([s for s in summary_lines if s])))

            thinking = self._get_plan_thinking(plan)
            if thinking:
                elements.append(card_element_zhedie("排查规划思路", [card_element_markdown(thinking)]))

        # 最近 worker 结果
        pairs = self._iter_worker_results(state)
        if pairs:
            lines = [f"- {name}: {conclusion}" for name, conclusion in pairs]
            elements.append(card_element_markdown("**最近结果**\n" + "\n".join(lines)))

        # Reporter 预览
        summary = self._get_reporter_summary(state)
        if summary:
            elements.append(card_element_markdown("**最终摘要(预览)**\n" + summary))

        # 详细过程：最近一条消息
        detail = self._get_last_message_detail(state)
        if detail:
            elements.append(card_element_zhedie("详细过程", [card_element_markdown(detail)]))

        if not elements:
            return [card_element_markdown("(无更新)")]
        return elements

    async def flush(self, force: bool = False):
        if self._latest_state is None and not force:
            return
        # 再次进行节流与配额校验
        now = time.time()
        # 清理一分钟窗口
        self._updates_in_minute = [t for t in self._updates_in_minute if now - t < 60]
        if not force:
            if len(self._updates_in_minute) >= self.config.max_updates_per_minute:
                return
            if (now - self._last_flush_ts) * 1000 < self.config.debounce_ms:
                return

        progress_mid = self._ensure_progress_card()
        elements = self._build_progress_elements_from_state(self._latest_state)

        # 更新卡片
        self.zlark.update_custom_card_msg(
            message_id=progress_mid,
            title="任务进度",
            subtitle="处理中",
            elements=elements,
        )

        # 记录配额与时间
        now = time.time()
        self._last_flush_ts = now
        self._updates_in_minute.append(now)
        # 持续基于最新 state 渲染，无需清空
