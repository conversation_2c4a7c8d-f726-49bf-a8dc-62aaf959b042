### DAG 规划与调度设计

```mermaid
graph TB
  subgraph Plan["DAG 规划 (planner)"]
    G["goal"] --> TS["tasks: id / depends_on / query_params"]
    TS --> SCHEMA["DAGPlanSchema"]
    SCHEMA --> PRE["预取数据: DataCenter.batch"]
    PRE --> PLAN["DAGPlan: worker_params 已注入"]
  end

  subgraph Route["路由/并发 (Functional)"]
    PLAN --> RDY["get_ready_tasks_by_results()"]
    RDY -->|并发执行; limit = max_parallel_workers| WRK["worker_task"]
    RDY -. "无可执行" .-> REPLAN["回到 planner_task 聚合/再规划"]
  end

  subgraph Normalize["规范化与校验"]
    DEDUP["按 id 去重"]
    SAN["依赖清洗: 移非法/自依赖"]
    CYCLE["断环: Kahn"]
    LAYER["分层: compute_dependency_layers"]
  end

  PLAN --> DEDUP --> SAN --> CYCLE --> LAYER
  WRK --> PLAN
  REPLAN --> PLAN
  PLAN --> RPT["reporter → ReporterResult"]
```

#### 模型定义（`src/langgraph/nodes/common/types.py`）
- `DAGPlan`：一次完整计划（`goal`/`tasks`/`thinking`）。
- `DAGPlanTask`：单个任务，字段：`id/depends_on/status/worker_params`。
- `DAGPlanSchema`/`DAGPlanTaskSchema`：规划阶段的轻量规范，包含 `depends_on + query_params`，运行时补齐 `worker_params`。

#### 规划生成（`src/langgraph/tasks/task_planner.py`）
- 使用 `PromptBuilder` 构建提示词并请求 LLM 生成 DAGPlanSchema。
- `DataCenter.batch_query_and_store_data` 预取所有任务数据，得到 `WorkerParams` 列表并写入各 `DAGPlanTask.worker_params`。
- 生成并规范化 `DAGPlan`（去重与依赖清洗），写入 `state.plan` 并产出计划消息。

#### 路由与并发（Functional）
- 若 `plan is None`：调用 `planner_task` 生成计划。
- 若 `plan` 存在：
  - 通过 `get_ready_tasks_by_results()` 收集依赖满足的任务；按 `max_parallel_workers` 并发执行 `worker_task`。
  - 完成后聚合 state 更新；若无可执行任务：调用 `planner_task` 聚合/是否继续分析的判定；所有任务完成后进入 `reporter_task`。

#### Worker 执行（`AnalysisWorkerNode`）
- 确保数据：从 `DataCenter` 取数或实时查询。
- 失败自适应：请求 LLM 生成 `RetryParams` 决定是否调整 `DataQueryParams` 并重试一次。
- 产出 `WorkerResult` 写入 `messages` 与 `worker_results`；同步更新 `worker_params_results`。

#### 汇报（`reporter_core.reporter_node`）
- 生成 `ReporterResult`（结构化汇报），写入 `state.reporter_result` 并生成消息。

#### 示例：最小 `DAGPlanSchema`
```json
{
  "goal": "诊断登录成功率异常",
  "thinking": "优先排查国家/ISP 维度",
  "tasks": [
    {
      "id": 1,
      "depends_on": [],
      "query_params": { "metric_name": "登录成功率", "time_start": "2025-08-10 00:00:00", "time_end": "2025-08-11 00:00:00" }
    },
    {
      "id": 2,
      "depends_on": [1],
      "query_params": { "metric_name": "登录错误码分布", "time_start": "2025-08-10 00:00:00", "time_end": "2025-08-11 00:00:00" }
    }
  ]
}
```

说明：规划阶段采用整数 `id` 进行依赖引用；运行时以 `query_params.to_key()` 作为唯一键。

#### 依赖规范化与环处理
- 依赖清洗：移除非法依赖与自依赖；断开环依赖（逐步移除一条环边）。
- 分层计算：`compute_dependency_layers` 基于 Kahn 算法输出层序，用于“先做/随后”摘要与并发评估。

#### 规划合并与多轮分析
- 在无 ready 任务时，`planner` 进行聚合/再规划：
  - 若返回新增任务：与现有计划合并（去重与依赖校验），并发继续
  - 若无新增任务：仅更新 `plan.thinking`，交由 `reporter` 汇总


