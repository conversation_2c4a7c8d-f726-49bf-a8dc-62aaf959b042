### QFlowAgent

面向数据问题诊断与多维度分析的 LangGraph 智能体。以“规划 → 并发分析 → 聚合/多轮再规划 → 汇报”的图谱为核心，内置统一数据中心、SQL 生成/执行链路与消息流转（本地/飞书）。

### 项目目标
- 提供可复用的“从问题到结论”的自动化分析流水线：记忆检索 → 规划/DAG → 并发分析 → 聚合与多轮再规划 → 汇报。
- 将“数据查询（SQL 生成/执行/错误码语义化）”与“LLM 分析”解耦，通过 `DataCenter` 统一存储与复用，降低重复查询成本。
- 基于 LangGraph，形成清晰的节点职责与边界，便于新增指标/维度、替换模型提供方与定制业务节点。
- 面向真实场景的稳健性：任务依赖调度、失败重试、错误语义化、经验库沉淀与检索。

### 文档目录
- [命令与开发指南](README_cmd.md)
- [LangGraph 节点流转图](README_nodes.md)
- [DAG 规划与调度设计](README_dag.md)
- [上下文与提示词工程](README_llm.md)
- [RAG 设计与经验库](README_rag.md)

---

### 快速开始

- 环境要求：Python 3.12+
- 安装依赖（建议本地使用 `uv`）

```bash
uv pip install -e .[dev]
```

- 启动 LangGraph Dev（基于 `langgraph.json`）

```bash
uv run langgraph dev
```

- 直接本地运行示例（流式打印 AI 输出）

```bash
uv run python apps/zego_test.py
```

- 飞书接入（需在 `.env` 配置飞书/LLM 等敏感变量）

```bash
uv run python apps/zego_lark.py
```

 

---

### 架构总览

- 图入口：`src/graph.py:zego_graph`（在 `langgraph.json` 中暴露）
- 状态定义：`src/state.py:State`
- 节点：
  - `planner`：生成计划（`DAGPlanSchema`/`DAGPlan`）与数据查询任务（支持 DAG 依赖），预取数据，交由 `planner_router` 并发派发
  - `worker`：基于 `current_task.worker_params` 执行统一分析逻辑（确保数据/失败自适应重试/LLM 分析），推进 `DAGPlanTask` 状态
  - `reporter`：对外汇报，输出结构化结论（生成 `state.reporter_result`）

- 流转：Functional 主循环依据依赖计算可执行任务，并发执行 `worker_task`；完成后回到 `planner_task` 判断是否重规划；流程在 `reporter_task` 结束。
- 并发：通过 `config.configurable.max_parallel_workers` 控制（默认 5）。

---

### 运行时数据流

1. 用户问题进入图（`messages`）。
2. `planner` 基于用户问题生成 `DAGPlanSchema` 并预取数据：
   - 生成 SQL（`src/zego_tools/sql_generator/generate_sql.py:generate_sql`）
   - 执行 SQL（指数退避重试与错误码提取）
   - 将结果沉淀至 Store（按 `thread_id` 命名空间）
   - 将参数映射为 `DAGPlanTask` 列表，产出 `DAGPlan` 追加写入 `state.plans`（列表，按时间顺序累积）
3. `planner_router` 调度：依据依赖并发派发可就绪任务到 `worker`。
4. `worker` 统一分析（确保数据 → 失败自适应重试 → LLM 分析），产出 `WorkerResult`，推进任务状态为 `RUNNING → DONE/FAILED`，回到 `planner`。
5. 无可执行任务时：`planner` 在节点内执行聚合/再规划；需要继续则合并新任务并回到并发派发；不需要则更新 `plan.thinking`，最终由 `reporter` 生成 `reporter_result` 并结束。

任务并发派发：`planner_router` 基于最新计划与已完成结果计算可执行任务并并发下发（`state.plans[-1]` 与 `worker_results`）。

数据中心（Store 命名空间按 `thread_id` 隔离）：`("data_center", "queried_data", thread_id)`。

---

### SQL 生成与执行

- 生成：`src/langgraph/zego_tools/sql_generator/generate_sql.py:generate_sql`
- 执行：`src/langgraph/zego_tools/ocean_executor/simple_executor.py:execute_sql_async`（指数退避重试 + 错误码抽取/格式化）
  - 签名：`execute_sql_async(type: Literal["ocean", "themis"], sql: str, max_retries: int = 3, base_delay: float = 1.0, themis_db: Optional[str] = None) -> SqlExecutorResult`
  - 示例：
    ```python
    from src.zego_tools.ocean_executor import execute_sql_async
    result = await execute_sql_async("ocean", "SELECT 1", max_retries=2, base_delay=0.5)
    # themis 多库示例：
    result = await execute_sql_async("themis", "SELECT * FROM t", themis_db="demo")
    ```
- 数据沉淀：`DataCenter.store_data` 将 `DataFrame` 与错误码说明写入 Store，供后续 LLM 提示词直接引用。
- 数据中心位置：`src/langgraph/nodes/common/data_center.py`

---

### 消息与可视化

- `DAGPlan`（通过 `to_message()`）展示计划摘要。
- `WorkerResult`：每个任务的分析结果。
- `ReporterResult`：最终结构化汇报（由 `reporter` 生成，写入 `state.reporter_result`）。
- 本地控制台（`apps/zego_test.py`）流式打印；飞书客户端（`apps/zego_lark.py`）以卡片形式展示。

---

### 流式与线程

- 使用 `astream`：
  - `stream_mode="updates"`（飞书侧逐节点增量）
  - `stream_mode="values"`（本地侧聚合值）
- 通过 `config={"configurable": {"thread_id": ..., "max_parallel_workers": 5}}` 隔离会话与控制并发。

---

### 开发与扩展

- 新增指标/维度：在 `src/langgraph/zego_tools/sql_generator/metrics.py` 定义指标、在 `sql_template.py` 增加模板，`DataQueryParams` 自动受用。
- 优化分析：通过 `src/langgraph/common/prompts/builder.py:PromptBuilder` 与 `ContextBuilder` 完成提示词与上下文工程。
- 拓展节点：
  - 复用 `AnalysisWorkerNode` 实现新类型分析；或在 `src/langgraph/graph.py` 增加自定义节点与路由。
- 接入新的 LLM 提供方：更新 `src/langgraph/common/models/model_list.py` 与 `src/langgraph/common/llms.py` 的映射/参数。
- 单元测试（SQL 生成）：

```bash
uv run bash tests/test_sqlgen.sh
```

---

### 目录一览（节选）

```
src/
  langgraph/
    graph.py               # 图谱编排入口
    state.py               # 状态结构与序列化
    tasks/
      task_planner.py      # 规划任务逻辑
      task_worker.py       # 通用分析任务逻辑
      task_reporter.py     # 汇报任务逻辑
    zego_tools/
      sql_generator/       # 指标/模板/参数 → SQL
      ocean_executor/      # SQL 执行与错误码处理（Ocean/StarRocks/MySQL + Themis，多库切换）
    common/                # LLM、工具与通用方法
apps/
  zego_cli.py              # 本地运行示例
  zego_lark.py             # 飞书长连客户端
```

---

### 已知事项

- 聚合与再规划在 `planner` 内完成；不存在独立 aggregator 节点。

---

### 配置

- 在根目录创建 `.env`，包括 LLM 与数据库等密钥配置；`langgraph.json` 会自动加载该文件。

---

如需进一步接入/重构，请按“开发与扩展”小节执行。



### 环境变量示例（.env）

> 说明：当前 `src/langgraph/common/models/model_list.py` 在导入时会校验多家厂商的基础配置（即便暂未使用）。数据库与代理配置用于 `src/langgraph/zego_tools/ocean_executor`。

```ini
# LLM Provider Base URLs & Keys（需全部提供以通过启动期校验）
DEEPSEEK_BASE_URL=
DEEPSEEK_API_KEY=
PPIO_BASE_URL=
PPIO_API_KEY=
ALI_BASE_URL=
ALI_API_KEY=
SHANGTANG_BASE_URL=
SHANGTANG_API_KEY=
OPENROUTER_BASE_URL=
OPENROUTER_API_KEY=
GLM_BASE_URL=
GLM_API_KEY=
KIMI_BASE_URL=
KIMI_API_KEY=

# StarRocks/MySQL（SQL 执行）
ZEGO_MYSQL_URL=
ZEGO_MYSQL_PORT=9030
ZEGO_MYSQL_DB=
ZEGO_MYSQL_USER=
ZEGO_MYSQL_PASSWORD=

# 可选：仅数据库走 SOCKS5 代理（LLM 请求自动避开代理）
ZEGO_SOCKS_PROXY=127.0.0.1
ZEGO_SOCKS_PORT=1080
ZEGO_SOCKS_PROXY_USER=
ZEGO_SOCKS_PROXY_PASSWORD=

# 飞书
LARK_APP_ID=
LARK_APP_SECRET=
```

---

### 网络与代理策略

- 数据库查询：通过 `aiomysql` 并配合按请求启用的 SOCKS5 代理（见 `src/langgraph/common/utils/socket_router.py:db_socks_context`）。
- LLM 请求：通过 `llm_request_context()` 临时禁用 SOCKS 代理，避免与数据库代理冲突。

---

### 失败重试与参数自适应

- SQL 执行：指数退避重试（最多 5 次），并将错误码语义化后存入 Store。
- Worker 分析：查询失败时，请求 LLM 分析错误上下文并可调整 `DataQueryParams` 重试一次。

---

### 测试

```bash
uv run bash tests/test_sqlgen.sh
```

---

### 额外说明

- 飞书客户端 `apps/zego_lark.py` 使用的环境变量由 `src/lark/zlark.py` 在模块内加载，路径为 `src/lark/.env`。若仅在根目录配置 `.env`，请同步在 `src/lark/.env` 放置所需 `LARK_*` 变量，或将 `zlark.py` 的 `load_dotenv` 指向根目录。

