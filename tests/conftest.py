import sys
from pathlib import Path

import pytest_asyncio


# Ensure project root is importable so `import src...` works in tests
ROOT = Path(__file__).resolve().parents[1]
if str(ROOT) not in sys.path:
    sys.path.insert(0, str(ROOT))


# 确保测试会话结束前优雅关闭全局 DB 连接池，避免 aiomysql 在事件循环关闭后析构告警
@pytest_asyncio.fixture(scope="session", autouse=True)
async def _shutdown_db_pools():
    yield
    try:
        from src.langgraph.zego_tools.ocean_executor.simple_connector import ocean_connection, themis_connection

        try:
            await ocean_connection.close()
        except Exception:
            pass
        try:
            await themis_connection.close()
        except Exception:
            pass
    except Exception:
        # 在无依赖环境或导入失败时忽略
        pass
