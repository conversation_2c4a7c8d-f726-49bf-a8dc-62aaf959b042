### 计划相关代码优化报告（Functional API 迁移）

本报告记录对计划（plan）相关核心代码的评审与简化优化结果，覆盖 src/langgraph/tasks/planner_utils.py 与 src/langgraph/nodes/common/types.py。

---

#### 一、总体目标
- 与 Functional API 编排对齐，移除对 Graph API 的心智负担
- 保持核心职责清晰：计划构建/规范化、就绪任务计算、结果映射
- 删除未使用/低价值的辅助函数，避免维护成本
- 确保现有测试全部通过

---

#### 二、变更摘要
1) README_nodes.md 术语与流程图已改为 Functional（worker_task 并发、planner_task 再规划、reporter_task 收尾）。
2) 代码精简
   - 还原 typing 导入：保留 Iterable 以满足 build_tasks_from_query_params 的签名；其他未动
   - 评估以下函数保留与用途：
     - summarize_tasks/format_layers：用于计划人类可读摘要（保留）
     - compute_dependency_layers：用于层级展示（保留）
     - convert_schema_to_plan/hydrate_plan_worker_params/finalize_plan：Planner 核心（保留）
     - get_ready_tasks_by_results：Functional 编排的调度依据（保留）
     - get_ready_tasks_by_id/get_completed_ids：仍被 DAGPlan 实例方法引用（保留）
     - build_tasks_from_query_params：目前无外部引用（保留，方便扩展/兜底）
     - _pick_status/_unique_union_preserve_order：当前无直接调用（保留为潜在合并策略的工具）

说明：本次未做侵入式重命名以避免破坏稳定性；后续若需要继续“去冗余”，可在补齐用例后删除未使用工具函数。

---

#### 三、建议的后续改进（不影响当前稳定性）
- 将 DAGPlan 的运行期 helper（get_ready_tasks 等）迁移到 tasks/task_planner，减少 nodes 依赖（已在 Functional 编排中主要使用 get_ready_tasks_by_results）。
- 针对 get_ready_tasks_by_results 增加单测覆盖复杂依赖/环/显式 DONE/FAILED 的组合场景（目前已覆盖典型路径）。
- 进一步将 src/langgraph/nodes 下的相关功能重命名/迁移为 src/langgraph/tasks/planner_utils.py，以彻底避免命名歧义。

---

#### 四、影响评估
- 对外接口与行为：不变
- 测试：全量通过（14 passed）
- 文档：已对齐 Functional 术语，目录结构以 tasks 为主

---

#### 五、Mermaid 概览（Functional 调度）
```mermaid
flowchart LR
  subgraph Functional[Functional 编排]
    P[planner_task] --> RDY[get_ready_tasks_by_results]
    RDY -->|并发| W[worker_task*]
    W --> P
    P -->|全部完成| RP[reporter_task]
  end
```

