# QFlowAgent Web 前端

## 🎯 功能特性

### ✅ 已实现功能
1. **任务创建与执行**
   - 通过Web界面创建分析任务
   - 实时WebSocket流式显示任务执行过程
   - 支持配置并行数和递归限制

2. **任务历史管理**
   - 基于langgraph checkpoint的历史查询
   - 任务列表筛选和搜索
   - 详细的任务执行历史展示

3. **现代化UI**
   - 默认暗黑模式（Element Plus官方支持）
   - 响应式设计，适配各种屏幕
   - 直观的任务状态展示

## 🏗️ 技术架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Vue 3 前端    │    │   FastAPI 后端   │    │  LangGraph 核心  │
│                 │    │                 │    │                 │
│ • Element Plus  │◄──►│ • REST API      │◄──►│ • zego_graph    │
│ • Pinia Store   │    │ • WebSocket     │    │ • Checkpoint    │
│ • Vue Router    │    │ • CheckpointMgr │    │ • 任务执行      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📁 项目结构

```
src/
├── api/                    # FastAPI 后端
│   ├── main.py            # 应用入口
│   ├── checkpoint_manager.py  # Checkpoint管理器
│   ├── models/            # Pydantic数据模型
│   └── routes/            # API路由
│       ├── tasks.py       # 任务相关API
│       └── websocket.py   # WebSocket流式接口
├── web/                   # Vue 3 前端
│   ├── src/
│   │   ├── views/         # 页面组件
│   │   ├── stores/        # Pinia状态管理
│   │   ├── api/           # API服务
│   │   └── router/        # 路由配置
│   ├── package.json       # 前端依赖
│   └── vite.config.js     # Vite配置
└── langgraph/             # 现有的图逻辑（不变）
```

## 🚀 快速开始

### 1. 安装依赖

```bash
# 安装Python依赖（包含FastAPI）
uv sync

# 安装前端依赖
cd src/web
pnpm install
cd ../..
```

### 2. 启动服务

```bash
# 一键启动前后端服务
./scripts/run_server_and_web.sh
```

服务启动后：
- 前端地址: http://localhost:3000"
- 后端地址: http://localhost:2026"
- API文档: http://localhost:2026/docs"

### 3. 使用界面

1. **创建任务**: 在任务中心输入查询内容，点击"启动任务"
2. **实时监控**: 任务执行过程会实时显示在流式区域
3. **查看历史**: 在历史记录页面查看所有任务
4. **任务详情**: 点击任务ID查看详细的执行历史和checkpoint

## 🔧 核心设计原则

### 1. 复用现有架构
- **不重复造轮子**: 直接调用现有的 `zego_graph()`
- **保持一致性**: 使用相同的 `thread_id` 和 `config` 机制
- **零破坏性**: 不修改现有的CLI功能

### 2. 简洁的API设计
```python
# 只需4个核心接口
POST   /api/tasks                    # 启动任务
GET    /api/tasks                    # 任务列表  
GET    /api/tasks/{thread_id}        # 任务详情
WebSocket /api/tasks/{thread_id}/stream  # 实时流
```

### 3. CheckpointManager职责
- ✅ 查询langgraph checkpoint store
- ✅ 格式化任务历史数据
- ❌ 不重新实现任务执行逻辑
- ❌ 不创建新的状态存储

## 📊 实时流式功能

WebSocket消息类型：
- `start`: 任务开始
- `update`: 图节点更新
- `ai_message`: AI回复消息
- `error`: 错误信息
- `complete`: 任务完成

## 🎨 UI特性

### 暗黑模式
- 默认启用Element Plus官方暗黑主题
- 支持一键切换明暗模式
- 所有组件完美适配暗黑模式

### 响应式设计
- 适配桌面端和移动端
- 灵活的布局系统
- 优雅的加载和错误状态
