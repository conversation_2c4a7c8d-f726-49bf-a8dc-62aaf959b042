## 单元测试与验证指南（README_unitest）

本说明覆盖本项目当前的测试现状、运行方式、数据产物与最佳实践，便于在本地与CI环境稳定复现结果并逐步提升质量保障。

---

## 测试框架与依赖

- 运行时环境：Python 3.12+
- 框架组合：
  - pytest + pytest-asyncio（异步与跳过策略；图执行路径建议后续补充）
  - unittest（SQL 生成模块的大量用例）
- 依赖安装（推荐最小化测试集）：

```bash
uv pip install -e .[test]
# 若同时参与开发，可安装：uv pip install -e .[dev]
```

---

## 目录结构与测试范围

```
tests/
  conftest.py                  # 追加工程根路径，确保 import src...
  test_connectivity.py         # 异步连接性与LLM冒烟测试（pytest）
  src/
    test_sqlgen_full.py      # SQL 生成器覆盖性与稳定性测试（unittest）
  runall.sh                    # 一键生成日级SQL并执行SQL生成单测
  test_sqlgen.sh               # 仅执行 SQL 生成单测
  generate_daily_sql.sh        # 仅生成日级SQL（写入 tests/data/*/regular_queries.sql）
  data/                        # 测试产物与基线：
    <各指标目录>/regular_queries.sql
    test_sqlgen.sqlite         # SQL基线（用于回归对比）
    test_sqlgen.sqlite.old     # 旧基线备份
```

覆盖重点：
- SQL 生成模块 `src/langgraph/zego_tools/sql_generator/*` 的组合覆盖、输出结构与回归对比
- 基础连通性：数据库可用性检查、LLM 简单响应冒烟（具备健壮跳过策略）

尚未覆盖（建议逐步补齐）：
- 图执行路径与节点交互（planner/worker/reporter；使用内存型 Store 可避免外部依赖）
- DataCenter 与 Store 读写一致性
- SQL 执行错误语义化与重试策略的细粒度断言

---

## 快速运行

### 1) 仅运行 SQL 生成单测

```bash
uv run bash tests/test_sqlgen.sh
# 等价：uv run python -m unittest tests.src.test_sqlgen_full.TestFullSQLGeneration.test
```

特点：
- 使用 unittest，覆盖数百条以上 SQL 组合（断言 SELECT/FROM 存在等）
- 读取/更新 `tests/data/test_sqlgen.sqlite`，对比新旧 SQL 语义差异并输出统计
- 同步生成/更新各指标目录下的 `regular_queries.sql`

### 2) 生成日级 SQL 样例集（便于人工审阅/回归）

```bash
uv run bash tests/generate_daily_sql.sh
# 产物：tests/data/<指标名>/regular_queries.sql（按稳定规则排序）
```

### 3) 连接性与冒烟用例（pytest）

```bash
uv run pytest -q tests/test_connectivity.py
```

包含：
- test_db_connectivity：若未配置 `ZEGO_MYSQL_URL` 则自动跳过
- test_llm_greeting：若未配置任一 LLM Key 则自动跳过
- test_smoke_query_non_empty：依赖 DB，执行真实 SQL 并断言非空

### 4) 一键脚本

```bash
uv run bash tests/runall.sh
# 顺序：生成日级SQL → 执行 SQL 生成单测
```

---

## 环境变量与跳过策略

以下用例会在环境未准备时“跳过”而非失败：

- 数据库连通性与真实查询：
  - 关键变量：`ZEGO_MYSQL_URL`（以及 `ZEGO_MYSQL_PORT/DB/USER/PASSWORD` 等）
  - 未配置 → 跳过 `test_db_connectivity` 与 `test_smoke_query_non_empty`
- LLM 冒烟：
  - 可用任意一项即可：`ALI_API_KEY`、`DEEPSEEK_API_KEY`、`KIMI_API_KEY`、`GLM_API_KEY`、`OPENROUTER_API_KEY`
  - 均未配置 → 跳过 `test_llm_greeting`
- 代理策略：LLM 请求在测试时通过 `llm_request_context()` 临时规避 SOCKS 代理配置，避免与 DB 代理相互影响

环境变量模板可参考根目录 `README.md` 中的“.env 示例”。

---

## 覆盖率与选择性运行

使用 pytest 汇总覆盖率（可同时收集 unittest 用例的覆盖）：

```bash
uv run pytest -q --cov=src --cov-report=term-missing
```

选择性运行：

```bash
# 仅运行某个pytest用例
uv run pytest -q tests/test_connectivity.py::test_db_connectivity

# 通过 -k 选择子集
uv run pytest -q -k "connectivity or greeting"
```

提示：`tests/src/test_sqlgen_full.py` 体量较大，首次运行可能输出大量对比日志，建议本地先单独跑通过后再纳入覆盖率统计。

---

## 测试产物与基线文件

- `tests/data/<指标>/regular_queries.sql`
  - 一个指标一个文件夹，单文件 `regular_queries.sql`
  - 文件内附带查询参数 JSON 注释，便于审阅/对比
- `tests/data/test_sqlgen.sqlite`
  - 作为 SQL 基线库，回归时会与新生成结果比对
  - 若检测到差异：
    - 终端会打印“NEW/REMOVED/CHANGED”摘要，并自动备份旧库为 `*.old`
    - 确认差异合理后，将新的产物纳入版本控制

---

## 关键用例说明（摘录）

- tests/src/test_sqlgen_full.py（unittest）
  - TestFullSQLGeneration
    - 生成全量 SQL 组合（含粒度/维度/过滤），断言数量下限（>500）与基本语法片段
    - 与旧基线对比语义差异（借助 `sqlparse` 标准化），统计通过/失败/新增/删除
    - 同步写入 SQLite 与分指标 `regular_queries.sql`
  - TestDataQueryParams
    - 表名选择随时间跨度切换（1min/10min/1hour/1day）
    - where 条件与过滤项替换（appid/country/error_code→metric.error_field）
    - 非法维度校验（Pydantic/生成阶段二选一触发异常）
  - TestSaveToSqlFileStructure
    - 断言“一个指标一个文件夹/一个文件”的输出结构与内容注释

- tests/test_connectivity.py（pytest + asyncio）
  - test_db_connectivity：数据库可用性
  - test_llm_greeting：最小 LLM 响应有效性
  - test_smoke_query_non_empty：真实 SQL 路径非空校验

---

## CI 建议（可直接复用的最小工作流）

```yaml
name: unit-tests
on:
  push:
  pull_request:
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: astral-sh/setup-uv@v4
      - run: uv python install 3.12
      - run: uv pip install -e .[test]
      # 可选：注入少量 LLM/DB 环境便于覆盖率更高；未配置则相关用例自动跳过
      - run: uv run pytest -q --cov=src --cov-report=term-missing
```

如需校验 SQL 基线稳定性，建议在单独 Job 中执行：

```yaml
  sqlgen:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: astral-sh/setup-uv@v4
      - run: uv python install 3.12
      - run: uv pip install -e .[test]
      - run: uv run python -m unittest tests.src.test_sqlgen_full.TestFullSQLGeneration.test
```

---

## 初步评估与改进方向

- 优点：
  - SQL 生成覆盖面广、产物可读（带参数注释）且具备稳定排序，便于审阅与回归
  - 连接性与LLM用例具备完备的跳过策略，兼容无密钥/无DB的环境
  - 脚本化入口（runall/test/generate）降低了使用门槛

- 可改进：
  - 增加对 `src/langgraph/zego_tools/ocean_executor/*` 的错误语义化与重试分支的细粒度断言（可通过注入假连接/桩对象）
  - 为 `nodes/planner/*` 与 `nodes/worker/*` 引入最小闭环的集成测试（使用内存型 Store，禁用外部依赖）
  - 将 SQL 基线对比纳入 PR 检查，并产出差异摘要工件，减少人工对比成本
  - 对关键公共函数添加快速属性测试（hypothesis）提升鲁棒性（可选）

---

## 常用问题排查

- 首次运行 SQL 生成测试时输出“差异很多”：
  - 属于正常现象（首次建立基线）。确认 SQL 合理后，提交 `tests/data` 变更
- CI 上 LLM/DB 用例被跳过：
  - 未注入凭据/连接串即会跳过，不影响整体通过率；如需提升覆盖率，请在仓库机密中配置必要变量
- 本地代理影响 LLM 测试：
  - 已通过 `llm_request_context()` 在用例内规避 SOCKS 代理；若仍异常请检查系统级代理或环境变量


