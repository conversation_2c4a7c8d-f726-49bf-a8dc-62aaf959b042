### 上下文与提示词工程（LLM）

本节描述当前项目的上下文工程与提示词工程实现方式，覆盖多轮分析、并发调度背景下的上下文裁剪、JSON Schema 输出与统一提示词拼装能力。

```mermaid
flowchart TD
  subgraph Ctx["上下文构建 ContextBuilder"]
    UMsg["最后一条用户消息"]
    Goal["state.plan.goal"]
    Think["state.plan.thinking"]
    DimSum["最近维度结论摘要\n(从 messages 提取 WorkerResult)"]
  end

  subgraph Prompt["提示词构建 PromptBuilder"]
    Time["add_current_time_header"]
    Role["预置角色/任务/策略"]
    JSON["add_json_output_schema"]
  end

  UMsg -->|add_human_msg| Ctx
  Goal -->|add_plan_goal| Ctx
  Think -->|add_plan_thinking| Ctx
  DimSum -->|add_recent_dimension_summary| Ctx

  Ctx --> M["SystemMessage[]"]
  Prompt --> PText["Prompt String"]

  M --> LLM["structured_request(json_mode)"]
  PText --> LLM
  LLM --> Out["结构化对象 + usage_metadata\n(input_tokens/output_tokens)"]
```

### 统一上下文构建

- 位置：`src/langgraph/common/utils/context_builder.py:ContextBuilder`
- 组成：
  - 最后一条用户消息（可选）
  - 计划目标与规划阶段思考（来自 `state.plan`）
  - 最近分析结论摘要（从 `messages` 中提取 `WorkerResult` 对象）
- 用法：
  - `add_human_msg()`、`add_plan_goal()`、`add_plan_thinking()`、`add_recent_dimension_summary()` 组合构造上下文片段
  - `build_messages(prompt=...)` 生成 `SystemMessage` 列表作为 LLM 入参
  - `planner/worker/reporter` 均通过该构建器注入必要上下文，规模受控、可复现

### 提示词构建

- 位置：
  - `src/langgraph/common/prompts/builder.py:PromptBuilder`
  - `src/langgraph/common/prompts/fragments.py:*`
- 能力：
  - `add_current_time_header()` 注入统一时间戳头部
  - `add_text()`/`add_section()` 拼装可复用片段
  - `add_json_output_schema(schema)` 注入 JSON Schema 输出要求（以 Markdown 代码块展示 Schema）
- 预置入口：
  - `add_base_analysis_role()`：通用分析角色/任务/策略/输出要求
  - `add_ocean_dimension_tips()`：表维度说明
  - `add_rtc_special_issue()`：特殊地区说明
  - `add_reporter_base()`：汇报角色、任务、原则与输出要求

### 结构化输出与 Token 统计

- 位置：`src/langgraph/common/utils/llm_request_utils.py:structured_request`
- 特性：
  - 统一 `with_structured_output(..., method="json_mode", include_raw=True)`，解析原始与结构化结果
  - 自动统计 `usage_metadata`（`input_tokens`/`output_tokens`），并通过 `create_token_update()` 回写到 `state`
  - 超过阈值的输入 Token 直接拒绝（128k）
  - 带重试的解析流程（最多 3 次）
  - 网络隔离：`llm_request_context()` 强制直连，规避 SOCKS 代理影响

### 多轮分析与上下文裁剪

- `planner` 初次生成 `DAGPlanSchema` 后将计划写入 `state.plan` 并预取数据；
- 当无可执行任务时，`planner` 进入聚合/再规划：
  - 通过 `ContextBuilder` 注入“所有分析结论摘要”构建上下文
  - 请求 LLM 生成新的 `DAGPlanSchema` 并合并为新的 `DAGPlan`（或仅更新 `thinking`）
- `worker` 在执行分析前：
  - 注入“依赖任务的分析结论摘要”（通过 `current_work_id` 定位依赖链）
  - 在查询失败时构造错误上下文，请求 LLM 产出 `RetryParams` 决策重试

### 提示词中的数据注入

- `WorkerParams.get_data_prompt()`：
  - 成功：注入 `<数据说明>` 与 `<数据表>`（显式表头），并在大数据量时提示被 limit
  - 失败：注入失败摘要；错误解释由 `SqlExecutorResult.additional_info` 补充
  - 特殊说明：错误码说明会作为独立段落拼入

### 与并发/路由的协同

- 上下文与提示词在并发派发下按需裁剪，避免无关内容污染：
  - `worker` 仅带入与当前任务依赖相关的维度摘要
  - `reporter` 汇总所有任务结论
  - `planner` 在再规划时汇聚必要摘要并控制输出为 Schema

### 参考入口

- `src/langgraph/tasks/task_planner.py`：规划任务相关函数
- `src/langgraph/tasks/task_worker.py`：`AnalysisWorkerNode`
- `src/langgraph/tasks/task_reporter.py`：汇报任务相关函数


