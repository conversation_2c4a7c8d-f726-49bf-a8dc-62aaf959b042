------- 8/19-1-start
你需要回忆起：这是我们的原始任务：
> 现在数据库的连接代码非常非常复杂，现在希望简化
> 1. 执行sql时，排队顺序执行，更安全，不要搞什么并发连接池，又复杂又危险，还有熔断之类的复杂逻辑
> 2. 保留sockets相关的处理
> 3. 能否只使用一个python库来处理连接？现在很多fallback处理可能是由于sockets管理过于复杂导致的技术债，我们倾向于只保留aiomysql

------- 8/19-1-1
1. 你需要通读原有连接器的代码，评估你的修改是否引入了其他风险
2. 从你的调试过程来看，数据库连接失败后，Langgraph仍然继续执行了，这不符合我们的健康检查目的，请修复健康检查的功能


------- 820

消息队列？
智能上下文选择器？

- 前端展示历史分析
- 批量执行飞书任务

common
    - 简化模型管理 ✅
    - 简化上下文管理（统一使用一个context_builder.py）
    - 区分消息为 System、Human、ai， 优化llm注意力


langgraph
    - 生成曲线图,给模型或人参考？  图的token可能比数据表便宜的多? 要测测一个图token占用情况

service
    - 测试 checkpoint_manager
    - 
web 
    - 展示历史任务
    - 批量执行任务

lark
    - 获取消息历史，与本地对比？
    - 检索消息历史，获取问题数据库？


配置 temperature

能否将worker的工作信息，添加到AIMessage中，而不是System或HumanMessage


现在前端路由有点多余
1. 任务中心，最下方有一个简略的最近任务
2. 历史纪录，有完整的任务列表
这两个应该能合并吧？